<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.marketing.mapper.PaymentSettlementRecordSummaryMapper">

    <resultMap type="PaymentSettlementRecordSummary" id="PaymentSettlementRecordSummaryResult">
        <result property="id"    column="id"    />
        <result property="paymentRecordId"    column="payment_record_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="liveStatus"    column="live_status"    />
        <result property="careLevel"    column="care_level"    />
        <result property="bedNumber"    column="bed_number"    />
        <result property="totalPayment"    column="total_payment"    />
        <result property="roomType"    column="room_type"    />
        <result property="contractNumber"    column="contract_number"    />
        <result property="dischargeDate"    column="discharge_date"    />
        <result property="dischargeDateFormatted"    column="discharge_date_formatted"    />
        <result property="feeCalculationDetails"    column="fee_calculation_details"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPaymentSettlementRecordSummaryVo">
        select id, payment_record_id, customer_name, live_status, care_level, bed_number, total_payment, room_type, contract_number, discharge_date, discharge_date_formatted, fee_calculation_details, del_flag, create_by, create_time, update_by, update_time, remark from t_payment_settlement_record_summary
    </sql>

    <select id="selectPaymentSettlementRecordSummaryList" parameterType="PaymentSettlementRecordSummary" resultMap="PaymentSettlementRecordSummaryResult">
        <include refid="selectPaymentSettlementRecordSummaryVo"/>
        <where>
            <if test="paymentRecordId != null  and paymentRecordId != ''"> and payment_record_id = #{paymentRecordId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="liveStatus != null  and liveStatus != ''"> and live_status = #{liveStatus}</if>
            <if test="careLevel != null  and careLevel != ''"> and care_level like concat('%', #{careLevel}, '%')</if>
            <if test="bedNumber != null  and bedNumber != ''"> and bed_number like concat('%', #{bedNumber}, '%')</if>
            <if test="roomType != null  and roomType != ''"> and room_type like concat('%', #{roomType}, '%')</if>
            <if test="contractNumber != null  and contractNumber != ''"> and contract_number like concat('%', #{contractNumber}, '%')</if>
            <if test="params.beginDischargeDate != null and params.beginDischargeDate != '' and params.endDischargeDate != null and params.endDischargeDate != ''"> and discharge_date between #{params.beginDischargeDate} and #{params.endDischargeDate}</if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>

    <select id="selectPaymentSettlementRecordSummaryById" parameterType="String" resultMap="PaymentSettlementRecordSummaryResult">
        <include refid="selectPaymentSettlementRecordSummaryVo"/>
        where id = #{id}
    </select>

    <select id="selectPaymentSettlementRecordSummaryByPaymentRecordId" parameterType="String" resultMap="PaymentSettlementRecordSummaryResult">
        <include refid="selectPaymentSettlementRecordSummaryVo"/>
        where payment_record_id = #{paymentRecordId} and del_flag = '0'
        limit 1
    </select>

    <insert id="insertPaymentSettlementRecordSummary" parameterType="PaymentSettlementRecordSummary">
        insert into t_payment_settlement_record_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="paymentRecordId != null">payment_record_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="liveStatus != null">live_status,</if>
            <if test="careLevel != null">care_level,</if>
            <if test="bedNumber != null">bed_number,</if>
            <if test="totalPayment != null">total_payment,</if>
            <if test="roomType != null">room_type,</if>
            <if test="contractNumber != null">contract_number,</if>
            <if test="dischargeDate != null">discharge_date,</if>
            <if test="dischargeDateFormatted != null">discharge_date_formatted,</if>
            <if test="feeCalculationDetails != null">fee_calculation_details,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="paymentRecordId != null">#{paymentRecordId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="liveStatus != null">#{liveStatus},</if>
            <if test="careLevel != null">#{careLevel},</if>
            <if test="bedNumber != null">#{bedNumber},</if>
            <if test="totalPayment != null">#{totalPayment},</if>
            <if test="roomType != null">#{roomType},</if>
            <if test="contractNumber != null">#{contractNumber},</if>
            <if test="dischargeDate != null">#{dischargeDate},</if>
            <if test="dischargeDateFormatted != null">#{dischargeDateFormatted},</if>
            <if test="feeCalculationDetails != null">#{feeCalculationDetails},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePaymentSettlementRecordSummary" parameterType="PaymentSettlementRecordSummary">
        update t_payment_settlement_record_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="paymentRecordId != null">payment_record_id = #{paymentRecordId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="liveStatus != null">live_status = #{liveStatus},</if>
            <if test="careLevel != null">care_level = #{careLevel},</if>
            <if test="bedNumber != null">bed_number = #{bedNumber},</if>
            <if test="totalPayment != null">total_payment = #{totalPayment},</if>
            <if test="roomType != null">room_type = #{roomType},</if>
            <if test="contractNumber != null">contract_number = #{contractNumber},</if>
            <if test="dischargeDate != null">discharge_date = #{dischargeDate},</if>
            <if test="dischargeDateFormatted != null">discharge_date_formatted = #{dischargeDateFormatted},</if>
            <if test="feeCalculationDetails != null">fee_calculation_details = #{feeCalculationDetails},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaymentSettlementRecordSummaryById" parameterType="String">
        delete from t_payment_settlement_record_summary where id = #{id}
    </delete>

    <delete id="deletePaymentSettlementRecordSummaryByIds" parameterType="String">
        delete from t_payment_settlement_record_summary where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deletePaymentSettlementRecordSummaryByPaymentRecordId" parameterType="String">
        delete from t_payment_settlement_record_summary where payment_record_id = #{paymentRecordId}
    </delete>
</mapper>
