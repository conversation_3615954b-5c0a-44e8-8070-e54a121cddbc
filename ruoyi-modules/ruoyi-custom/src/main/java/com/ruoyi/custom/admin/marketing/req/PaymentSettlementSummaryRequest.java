package com.ruoyi.custom.admin.marketing.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 保存结算单汇总信息请求对象
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@ApiModel(value = "PaymentSettlementSummaryRequest", description = "保存结算单汇总信息请求对象")
public class PaymentSettlementSummaryRequest {

    /**
     * 应付/退床位费计算详情
     */
    @ApiModelProperty(value = "床位费计算详情，如：1500*6*0.5=4500元（2024/7/31至2025/7/30）")
    private String bedFeeDetails;

    /**
     * 应付/退护理费计算详情
     */
    @ApiModelProperty(value = "护理费计算详情，如：600元（2024/7/31至2024/7/30）")
    private String careFeeDetails;

    /**
     * 应付/退餐费计算详情
     */
    @ApiModelProperty(value = "餐费计算详情，如：800（2024/7/31至2024/7/30）")
    private String mealFeeDetails;

    /**
     * 应付/退空调费计算详情
     */
    @ApiModelProperty(value = "空调费计算详情，如：200+200/31*16=303（2024/7/31至2024/9/15）")
    private String airConditioningFeeDetails;

    /**
     * 以上费用合计计算详情
     */
    @ApiModelProperty(value = "以上费用合计计算详情")
    private String totalFeeDetails;

    /**
     * 应退未用及押金合计
     */
    @ApiModelProperty(value = "应退未用及押金合计，如：3000元")
    private String medicalDepositDetails;

}
