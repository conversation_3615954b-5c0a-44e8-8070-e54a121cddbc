package com.ruoyi.custom.admin.marketing.resp;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 离园管理列表响应对象
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel(value = "离园管理列表")
public class PaymentSettlementManagementResp {

    /**
     * 结算单id
     */
    @ApiModelProperty(value = "结算单id")
    private String paymentSettlementId;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号")
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 长者姓名
     */
    @Excel(name = "长者姓名")
    @ApiModelProperty(value = "长者姓名")
    private String customerName;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String elderlyPhone;

    /**
     * 客户状态（入住状态）
     */
    @Excel(name = "客户状态")
    @ApiModelProperty(value = "客户状态")
    private String liveStatus;

    /**
     * 护理级别
     */
    @Excel(name = "护理级别")
    @ApiModelProperty(value = "护理级别")
    private String careLevel;

    /**
     * 房间类型
     */
    @Excel(name = "房间类型")
    @ApiModelProperty(value = "房间类型")
    private String roomType;

    /**
     * 房间（床位号）
     */
    @Excel(name = "房间")
    @ApiModelProperty(value = "房间")
    private String bedNumber;

    /**
     * 状态，字典：custom_payment_record_status；0：暂存，1：已确认
     */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private String paymentStatus;
}
