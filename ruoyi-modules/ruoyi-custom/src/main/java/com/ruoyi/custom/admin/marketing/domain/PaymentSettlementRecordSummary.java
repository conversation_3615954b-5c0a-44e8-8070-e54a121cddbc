package com.ruoyi.custom.admin.marketing.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 结算单汇总信息对象 t_payment_settlement_record_summary
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@ApiModel(value = "PaymentSettlementRecordSummary", description = "结算单汇总信息")
public class PaymentSettlementRecordSummary extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 结算记录ID，关联t_payment_record表
     */
    @ApiModelProperty(value = "结算记录ID")
    private String paymentRecordId;

    /**
     * 客户姓名
     */
    @Excel(name = "客户姓名")
    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    /**
     * 入住状态
     */
    @Excel(name = "入住状态")
    @ApiModelProperty(value = "入住状态")
    private String liveStatus;

    /**
     * 护理级别
     */
    @Excel(name = "护理级别")
    @ApiModelProperty(value = "护理级别")
    private String careLevel;

    /**
     * 床位号
     */
    @Excel(name = "床位号")
    @ApiModelProperty(value = "床位号")
    private String bedNumber;

    /**
     * 结算合计
     */
    @Excel(name = "结算合计")
    @ApiModelProperty(value = "结算合计")
    private BigDecimal totalPayment;

    /**
     * 房间类型
     */
    @Excel(name = "房间类型")
    @ApiModelProperty(value = "房间类型")
    private String roomType;

    /**
     * 合同号
     */
    @Excel(name = "合同号")
    @ApiModelProperty(value = "合同号")
    private String contractNumber;

    /**
     * 离院日期
     */
    @ApiModelProperty(value = "离院日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dischargeDate;

    /**
     * 离院日期（yyyy年MM月dd日）
     */
    @Excel(name = "离院日期")
    @ApiModelProperty(value = "离院日期")
    private String dischargeDateFormatted;

    /**
     * 费用计算详情
     */
    @Excel(name = "费用计算详情")
    @ApiModelProperty(value = "费用计算详情")
    private String feeCalculationDetails;

    /**
     * 逻辑删除标记（0：显示；1：隐藏）
     */
    private String delFlag;
}
