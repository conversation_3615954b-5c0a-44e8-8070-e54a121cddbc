package com.ruoyi.custom.admin.marketing.service;

import com.ruoyi.custom.admin.marketing.constant.FeeType;
import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;
import com.ruoyi.custom.admin.marketing.req.FeeStatisticsReq;
import com.ruoyi.custom.admin.marketing.resp.FeeStatisticsResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentDetailResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentManagementResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentRemindResp;
import com.ruoyi.custom.admin.marketing.resp.PaymentSettlementManagementResp;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 缴费确认单Service接口
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface IPaymentRecordService {
    /**
     * 查询缴费确认单
     *
     * @param id 缴费确认单主键
     * @return 缴费确认单
     */
    public PaymentRecord selectPaymentRecordById(String id);

    /**
     * 查询缴费确认单列表
     *
     * @param paymentRecord 缴费确认单
     * @return 缴费确认单集合
     */
    public List<PaymentRecord> selectPaymentRecordList(PaymentRecord paymentRecord);

    /**
     * 修改缴费确认单
     *
     * @param paymentRecord 缴费确认单
     * @return 结果
     */
    public int updatePaymentRecord(PaymentRecord paymentRecord);

    /**
     * 批量删除缴费确认单
     *
     * @param ids 需要删除的缴费确认单主键集合
     * @return 结果
     */
    public int deletePaymentRecordByIds(String[] ids);

    /**
     * 删除缴费确认单信息
     *
     * @param id 缴费确认单主键
     * @return 结果
     */
    public int deletePaymentRecordById(String id);

    /**
     * 根据合同编号查询最新一条缴费记录
     *
     * @param contractNumber
     * @return
     */
    PaymentRecord selectLastInfoByContractNumber(String contractNumber);

    /**
     * 根据合同id，生成账单确认信息
     *
     * @param contractNumber
     * @return
     */
    PaymentRecord generatePaymentInfo(String contractNumber);

    /**
     * 缴费（直接确认）
     *
     * @param paymentRecord
     * @return
     */
    int payment(PaymentRecord paymentRecord);

    /**
     * 暂存缴费单
     *
     * @param paymentRecord 缴费记录
     * @return 结果
     */
    String draftPayment(PaymentRecord paymentRecord);

    /**
     * 确认缴费单
     *
     * @param id 缴费记录ID
     * @param billNumber 票据号
     * @param paidDetails 实缴详情
     * @param remark 备注
     * @return 结果
     */
    int confirmPayment(String id, String billNumber, List<PaymentRecord.PaidDetail> paidDetails, String remark);

    /**
     * 修改暂存缴费单
     *
     * @param paymentRecord 缴费记录
     * @return 结果
     */
    String updateDraftPayment(PaymentRecord paymentRecord);

    /**
     * 删除暂存缴费单
     *
     * @param id 缴费记录ID
     * @return 结果
     */
    int deleteDraftPayment(String id);

    /**
     * 缴费提醒列表
     *
     * @param paymentRecord
     * @return
     */
    List<PaymentRemindResp> paymentRemindList(PaymentRecord paymentRecord);

    /**
     * 根据合同id，生成账单结算信息
     *
     * @param contractNumber
     * @return
     */
    PaymentRecord generatePaymentSettlementInfo(String contractNumber);

    /**
     * 暂存结算单
     *
     * @param paymentRecord 结算记录
     * @return 结果
     */
    String draftSettlement(PaymentRecord paymentRecord);

    /**
     * 确认结算单
     *
     * @param id 结算记录ID
     * @return 结果
     */
    int confirmSettlement(String id);

    /**
     * 修改暂存结算单
     *
     * @param paymentRecord 结算记录
     * @return 结果
     */
    String updateDraftSettlement(PaymentRecord paymentRecord);

    /**
     * 删除暂存结算单
     *
     * @param id 结算记录ID
     * @return 结果
     */
    int deleteDraftSettlement(String id);

    /**
     * 缴费结算
     *
     * @param paymentRecord
     * @return
     */
    List<FeeStatisticsResp> feeStatistics(FeeStatisticsReq paymentRecord);

    /**
     * 根据合同id，查询缴费记录
     *
     * @param contractNumber
     * @return
     */
    List<PaymentRecord> fetchPaymentRecords(String contractNumber);

    /**
     * 根据用户id，查询余额
     *
     * @param userId
     * @return
     */
    BigDecimal fetchUserBalance(String userId);

    /**
     * 根据缴费记录，获取最新的一条缴费记录
     * @param records
     * @return
     */
    PaymentRecord getLastPaymentRecord(List<PaymentRecord> records);

    /**
     * 根据缴费记录，计算开始日期
     * @param lastPaymentRecord
     * @param contractStartDate
     * @param feeType
     * @return
     */
    Date calculateStartDate(PaymentRecord lastPaymentRecord, Date contractStartDate, FeeType feeType);

    /**
     * 根据缴费记录，计算费用
     * @param records
     * @return
     */
    Map<String, BigDecimal> calculateTotalFees(List<PaymentRecord> records);

    /**
     * 结算
     * @param paymentRecord
     * @return
     */
    int settlement(PaymentRecord paymentRecord);

    /**
     * 根据缴费记录ID查询缴费明细
     *
     * @param recordId 缴费记录ID
     * @return 缴费明细列表
     */
    public List<PaymentDetailResp> selectPaymentDetailByRecordId(String recordId);

    /**
     * 获取收入分类统计
     *
     * @return 收入分类统计列表
     */
    List<Map<String, Object>> getIncomeClassification();

    /**
     * 获取各缴费方式累计缴费统计
     *
     * @return 各缴费方式累计缴费统计列表
     */
    List<Map<String, Object>> getPaymentMethodStatistics();

    /**
     * 查询离园管理列表
     *
     * @param paymentRecord 查询条件
     * @return 离园管理列表
     */
    List<PaymentSettlementManagementResp> selectPaymentSettlementManagementList(PaymentRecord paymentRecord);

    /**
     * 查询缴费管理列表
     *
     * @param paymentRecord 查询条件
     * @return 缴费管理列表
     */
    List<PaymentManagementResp> selectPaymentManagementList(PaymentRecord paymentRecord);
}

