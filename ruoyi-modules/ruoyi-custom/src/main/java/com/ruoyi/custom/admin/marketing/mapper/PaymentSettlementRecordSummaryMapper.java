package com.ruoyi.custom.admin.marketing.mapper;

import com.ruoyi.custom.admin.marketing.domain.PaymentSettlementRecordSummary;

import java.util.List;

/**
 * 结算单汇总信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface PaymentSettlementRecordSummaryMapper {
    /**
     * 查询结算单汇总信息
     *
     * @param id 结算单汇总信息主键
     * @return 结算单汇总信息
     */
    public PaymentSettlementRecordSummary selectPaymentSettlementRecordSummaryById(String id);

    /**
     * 查询结算单汇总信息列表
     *
     * @param paymentSettlementRecordSummary 结算单汇总信息
     * @return 结算单汇总信息集合
     */
    public List<PaymentSettlementRecordSummary> selectPaymentSettlementRecordSummaryList(PaymentSettlementRecordSummary paymentSettlementRecordSummary);

    /**
     * 根据结算记录ID查询汇总信息
     *
     * @param paymentRecordId 结算记录ID
     * @return 结算单汇总信息
     */
    public PaymentSettlementRecordSummary selectPaymentSettlementRecordSummaryByPaymentRecordId(String paymentRecordId);

    /**
     * 新增结算单汇总信息
     *
     * @param paymentSettlementRecordSummary 结算单汇总信息
     * @return 结果
     */
    public int insertPaymentSettlementRecordSummary(PaymentSettlementRecordSummary paymentSettlementRecordSummary);

    /**
     * 修改结算单汇总信息
     *
     * @param paymentSettlementRecordSummary 结算单汇总信息
     * @return 结果
     */
    public int updatePaymentSettlementRecordSummary(PaymentSettlementRecordSummary paymentSettlementRecordSummary);

    /**
     * 删除结算单汇总信息
     *
     * @param id 结算单汇总信息主键
     * @return 结果
     */
    public int deletePaymentSettlementRecordSummaryById(String id);

    /**
     * 批量删除结算单汇总信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePaymentSettlementRecordSummaryByIds(String[] ids);

    /**
     * 根据结算记录ID删除汇总信息
     *
     * @param paymentRecordId 结算记录ID
     * @return 结果
     */
    public int deletePaymentSettlementRecordSummaryByPaymentRecordId(String paymentRecordId);
}
