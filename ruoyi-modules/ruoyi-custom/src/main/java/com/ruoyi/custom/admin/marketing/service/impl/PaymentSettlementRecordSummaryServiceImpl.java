package com.ruoyi.custom.admin.marketing.service.impl;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.StrUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.security.utils.DictUtils;
import lombok.SneakyThrows;

import com.ruoyi.custom.admin.marketing.domain.PaymentRecord;
import com.ruoyi.custom.admin.marketing.domain.PaymentSettlementRecordSummary;
import com.ruoyi.custom.admin.marketing.dto.PaymentRecordDTO;
import com.ruoyi.custom.admin.marketing.mapper.ContractInfoMapper;
import com.ruoyi.custom.admin.marketing.mapper.PaymentRecordMapper;
import com.ruoyi.custom.admin.marketing.mapper.PaymentSettlementRecordSummaryMapper;
import com.ruoyi.custom.admin.marketing.req.PaymentSettlementSummaryRequest;
import com.ruoyi.custom.admin.marketing.service.IPaymentSettlementRecordSummaryService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 结算单汇总信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Service
public class PaymentSettlementRecordSummaryServiceImpl implements IPaymentSettlementRecordSummaryService {
    @Autowired
    private PaymentSettlementRecordSummaryMapper paymentSettlementRecordSummaryMapper;

    @Autowired
    private PaymentRecordMapper paymentRecordMapper;

    @Autowired
    private ContractInfoMapper contractInfoMapper;

    /**
     * 查询结算单汇总信息
     *
     * @param id 结算单汇总信息主键
     * @return 结算单汇总信息
     */
    @Override
    public PaymentSettlementRecordSummary selectPaymentSettlementRecordSummaryById(String id) {
        return paymentSettlementRecordSummaryMapper.selectPaymentSettlementRecordSummaryById(id);
    }

    /**
     * 查询结算单汇总信息列表
     *
     * @param paymentSettlementRecordSummary 结算单汇总信息
     * @return 结算单汇总信息
     */
    @Override
    public List<PaymentSettlementRecordSummary> selectPaymentSettlementRecordSummaryList(PaymentSettlementRecordSummary paymentSettlementRecordSummary) {
        return paymentSettlementRecordSummaryMapper.selectPaymentSettlementRecordSummaryList(paymentSettlementRecordSummary);
    }

    /**
     * 根据结算记录ID查询汇总信息
     *
     * @param paymentRecordId 结算记录ID
     * @return 结算单汇总信息
     */
    @Override
    public PaymentSettlementRecordSummary selectPaymentSettlementRecordSummaryByPaymentRecordId(String paymentRecordId) {
        return paymentSettlementRecordSummaryMapper.selectPaymentSettlementRecordSummaryByPaymentRecordId(paymentRecordId);
    }

    /**
     * 新增结算单汇总信息
     *
     * @param paymentSettlementRecordSummary 结算单汇总信息
     * @return 结果
     */
    @Override
    public int insertPaymentSettlementRecordSummary(PaymentSettlementRecordSummary paymentSettlementRecordSummary) {
        paymentSettlementRecordSummary.setCreateTime(DateUtils.getNowDate());
        return paymentSettlementRecordSummaryMapper.insertPaymentSettlementRecordSummary(paymentSettlementRecordSummary);
    }

    /**
     * 修改结算单汇总信息
     *
     * @param paymentSettlementRecordSummary 结算单汇总信息
     * @return 结果
     */
    @Override
    public int updatePaymentSettlementRecordSummary(PaymentSettlementRecordSummary paymentSettlementRecordSummary) {
        paymentSettlementRecordSummary.setUpdateTime(DateUtils.getNowDate());
        return paymentSettlementRecordSummaryMapper.updatePaymentSettlementRecordSummary(paymentSettlementRecordSummary);
    }

    /**
     * 批量删除结算单汇总信息
     *
     * @param ids 需要删除的结算单汇总信息主键
     * @return 结果
     */
    @Override
    public int deletePaymentSettlementRecordSummaryByIds(String[] ids) {
        return paymentSettlementRecordSummaryMapper.deletePaymentSettlementRecordSummaryByIds(ids);
    }

    /**
     * 删除结算单汇总信息信息
     *
     * @param id 结算单汇总信息主键
     * @return 结果
     */
    @Override
    public int deletePaymentSettlementRecordSummaryById(String id) {
        return paymentSettlementRecordSummaryMapper.deletePaymentSettlementRecordSummaryById(id);
    }

    /**
     * 根据结算记录ID删除汇总信息
     *
     * @param paymentRecordId 结算记录ID
     * @return 结果
     */
    @Override
    public int deletePaymentSettlementRecordSummaryByPaymentRecordId(String paymentRecordId) {
        return paymentSettlementRecordSummaryMapper.deletePaymentSettlementRecordSummaryByPaymentRecordId(paymentRecordId);
    }

    /**
     * 根据结算记录DTO和费用明细生成汇总信息
     *
     * @param paymentRecordDTO 结算记录DTO
     * @param summaryRequest 费用明细请求
     * @return 结算单汇总信息
     */
    @Override
    public PaymentSettlementRecordSummary generateSummaryFromDTO(PaymentRecordDTO paymentRecordDTO, PaymentSettlementSummaryRequest summaryRequest) {
        if (paymentRecordDTO == null) {
            throw new ServiceException("结算记录DTO不能为空");
        }

        PaymentSettlementRecordSummary summary = new PaymentSettlementRecordSummary();
        summary.setId(IdUtils.fastUUID());
        summary.setPaymentRecordId(summaryRequest.getPaymentRecordId());
        summary.setDelFlag("0");

        // 从PaymentRecordDTO获取基本信息
        summary.setCustomerName(paymentRecordDTO.getElderlyName());
        summary.setCareLevel(DictUtils.selectDictLabel("care_level", paymentRecordDTO.getCareLevel()));
        summary.setBedNumber(paymentRecordDTO.getBedName());
        summary.setContractNumber(paymentRecordDTO.getContractNumber());
        summary.setDischargeDate(paymentRecordDTO.getDischargeDate());

        // 格式化离院日期
        if (paymentRecordDTO.getDischargeDate() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
            summary.setDischargeDateFormatted(sdf.format(paymentRecordDTO.getDischargeDate()));
        }

        // 结算合计（从totalCost获取）
        summary.setTotalPayment(paymentRecordDTO.getTotalCost() != null ?
            paymentRecordDTO.getTotalCost() : BigDecimal.ZERO);

        // 从PaymentRecordDTO获取入住状态和房间类型
        summary.setLiveStatus(getLiveStatusName(paymentRecordDTO.getLiveState()));
        summary.setRoomType(paymentRecordDTO.getRoomTypeName());

        // 生成费用计算详情
        summary.setFeeCalculationDetails(generateFeeCalculationDetailsFromRequest(summaryRequest));

        return summary;
    }

    /**
     * 根据费用明细请求保存或更新汇总信息
     *
     * @param summaryRequest 费用明细请求
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveOrUpdateSummaryFromRequest(PaymentSettlementSummaryRequest summaryRequest) {
        if (summaryRequest == null || StrUtil.isBlank(summaryRequest.getPaymentRecordId())) {
            throw new ServiceException("结算记录ID不能为空");
        }

        // 通过结算记录ID获取PaymentRecord，然后获取合同号
        PaymentRecord paymentRecord = paymentRecordMapper.selectPaymentRecordById(summaryRequest.getPaymentRecordId());
        if (paymentRecord == null) {
            throw new ServiceException("结算记录不存在");
        }

        // 通过合同号获取PaymentRecordDTO
        PaymentRecordDTO paymentRecordDTO = contractInfoMapper.selectLiveInfo(paymentRecord.getContractNumber());
        if (paymentRecordDTO == null) {
            throw new ServiceException("无法获取结算记录详细信息");
        }

        // 查询是否已存在汇总信息
        PaymentSettlementRecordSummary existingSummary = selectPaymentSettlementRecordSummaryByPaymentRecordId(summaryRequest.getPaymentRecordId());

        if (existingSummary != null) {
            // 更新现有汇总信息
            PaymentSettlementRecordSummary updatedSummary = generateSummaryFromDTO(paymentRecordDTO, summaryRequest);
            updatedSummary.setId(existingSummary.getId());
            updatedSummary.setCreateBy(existingSummary.getCreateBy());
            updatedSummary.setCreateTime(existingSummary.getCreateTime());
            return updatePaymentSettlementRecordSummary(updatedSummary);
        } else {
            // 创建新的汇总信息
            PaymentSettlementRecordSummary newSummary = generateSummaryFromDTO(paymentRecordDTO, summaryRequest);
            return insertPaymentSettlementRecordSummary(newSummary);
        }
    }

    /**
     * 根据费用明细请求生成费用计算详情字符串
     */
    private String generateFeeCalculationDetailsFromRequest(PaymentSettlementSummaryRequest summaryRequest) {
        StringBuilder details = new StringBuilder();

        // 添加各项费用详情
        if (StrUtil.isNotBlank(summaryRequest.getBedFeeDetails())) {
            details.append("应付/退床位费：").append(summaryRequest.getBedFeeDetails()).append("\n\n");
        }

        if (StrUtil.isNotBlank(summaryRequest.getCareFeeDetails())) {
            details.append("应付/退护理费：").append(summaryRequest.getCareFeeDetails()).append("\n\n");
        }

        if (StrUtil.isNotBlank(summaryRequest.getMealFeeDetails())) {
            details.append("应付/退餐费：").append(summaryRequest.getMealFeeDetails()).append("\n\n");
        }

        if (StrUtil.isNotBlank(summaryRequest.getAirConditioningFeeDetails())) {
            details.append("应付/退空调费：").append(summaryRequest.getAirConditioningFeeDetails()).append("\n\n");
        }

        if (StrUtil.isNotBlank(summaryRequest.getTotalFeeDetails())) {
            details.append("以上费用合计：").append(summaryRequest.getTotalFeeDetails()).append("\n\n");
        }

        if (StrUtil.isNotBlank(summaryRequest.getMedicalDepositDetails())) {
            details.append("应退未用及押金合计：").append(summaryRequest.getMedicalDepositDetails());
        }

        return details.toString();
    }

    /**
     * 获取入住状态名称
     */
    private String getLiveStatusName(String state) {
        if (StrUtil.isBlank(state)) {
            return "未知";
        }

        switch (state) {
            case "0":
                return "入住中";
            case "1":
                return "未入住";
            case "2":
                return "请假中";
            case "3":
                return "已退住";
            default:
                return "未知";
        }
    }

    /**
     * 导出结算单确认单docx文档
     *
     * @param paymentSettlementRecordSummary 结算单汇总信息
     * @param response HTTP响应
     */
    @SneakyThrows
    @Override
    public void exportSettlementConfirmationReport(PaymentSettlementRecordSummary paymentSettlementRecordSummary, HttpServletResponse response) {
        /**
         * 准备模板数据
         */
        Map<String, Object> templateData = new HashMap<>();

        // 根据字段对应规则设置数据
        templateData.put("ddf", paymentSettlementRecordSummary.getDischargeDateFormatted() != null ?
            paymentSettlementRecordSummary.getDischargeDateFormatted() : "");
        templateData.put("cn", paymentSettlementRecordSummary.getCustomerName() != null ?
            paymentSettlementRecordSummary.getCustomerName() : "");
        templateData.put("ls", paymentSettlementRecordSummary.getLiveStatus() != null ?
            paymentSettlementRecordSummary.getLiveStatus() : "");
        templateData.put("bn", paymentSettlementRecordSummary.getBedNumber() != null ?
            paymentSettlementRecordSummary.getBedNumber() : "");
        templateData.put("cl", paymentSettlementRecordSummary.getCareLevel() != null ?
            paymentSettlementRecordSummary.getCareLevel() : "");
        templateData.put("tp", paymentSettlementRecordSummary.getTotalPayment() != null ?
            paymentSettlementRecordSummary.getTotalPayment().toString() : "0");
        templateData.put("rt", paymentSettlementRecordSummary.getRoomType() != null ?
            paymentSettlementRecordSummary.getRoomType() : "");
        templateData.put("fcd", paymentSettlementRecordSummary.getFeeCalculationDetails() != null ?
            paymentSettlementRecordSummary.getFeeCalculationDetails() : "");
        templateData.put("ctn", paymentSettlementRecordSummary.getContractNumber() != null ?
            paymentSettlementRecordSummary.getContractNumber() : "");

        /**
         * 配置插件，渲染模板
         */
        Configure config = Configure.builder().build();

        XWPFTemplate template = XWPFTemplate
                .compile(ResourceUtil.getStream("templates/settlementConfirm.docx"), config)
                .render(templateData);

        /**
         * 设置响应头，触发浏览器下载
         */
        String fileName = "驻马店市为老服务中心结算确认单.docx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

        response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        response.setContentType("application/octet-stream");
        // 使用 'filename*' 提供 utf-8 编码支持
        response.setHeader("Content-disposition", "attachment; filename=" + encodedFileName + ";filename*=utf-8''" + encodedFileName);

        /**
         * 输出到响应流
         */
        try (OutputStream out = response.getOutputStream();
             BufferedOutputStream bos = new BufferedOutputStream(out)) {
            template.write(bos);
            bos.flush();
        }

        // 关闭模板资源
        template.close();
    }
}
