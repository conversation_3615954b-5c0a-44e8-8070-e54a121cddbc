package com.ruoyi.custom.admin.marketing.service;

import com.ruoyi.custom.admin.marketing.domain.PaymentSettlementRecordSummary;
import com.ruoyi.custom.admin.marketing.dto.PaymentRecordDTO;
import com.ruoyi.custom.admin.marketing.req.PaymentSettlementSummaryRequest;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 结算单汇总信息Service接口
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface IPaymentSettlementRecordSummaryService {
    /**
     * 查询结算单汇总信息
     *
     * @param id 结算单汇总信息主键
     * @return 结算单汇总信息
     */
    public PaymentSettlementRecordSummary selectPaymentSettlementRecordSummaryById(String id);

    /**
     * 查询结算单汇总信息列表
     *
     * @param paymentSettlementRecordSummary 结算单汇总信息
     * @return 结算单汇总信息集合
     */
    public List<PaymentSettlementRecordSummary> selectPaymentSettlementRecordSummaryList(PaymentSettlementRecordSummary paymentSettlementRecordSummary);

    /**
     * 根据结算记录ID查询汇总信息
     *
     * @param paymentRecordId 结算记录ID
     * @return 结算单汇总信息
     */
    public PaymentSettlementRecordSummary selectPaymentSettlementRecordSummaryByPaymentRecordId(String paymentRecordId);

    /**
     * 新增结算单汇总信息
     *
     * @param paymentSettlementRecordSummary 结算单汇总信息
     * @return 结果
     */
    public int insertPaymentSettlementRecordSummary(PaymentSettlementRecordSummary paymentSettlementRecordSummary);

    /**
     * 修改结算单汇总信息
     *
     * @param paymentSettlementRecordSummary 结算单汇总信息
     * @return 结果
     */
    public int updatePaymentSettlementRecordSummary(PaymentSettlementRecordSummary paymentSettlementRecordSummary);

    /**
     * 批量删除结算单汇总信息
     *
     * @param ids 需要删除的结算单汇总信息主键集合
     * @return 结果
     */
    public int deletePaymentSettlementRecordSummaryByIds(String[] ids);

    /**
     * 删除结算单汇总信息信息
     *
     * @param id 结算单汇总信息主键
     * @return 结果
     */
    public int deletePaymentSettlementRecordSummaryById(String id);

    /**
     * 根据结算记录ID删除汇总信息
     *
     * @param paymentRecordId 结算记录ID
     * @return 结果
     */
    public int deletePaymentSettlementRecordSummaryByPaymentRecordId(String paymentRecordId);

    /**
     * 根据结算记录DTO和费用明细生成汇总信息
     *
     * @param paymentRecordDTO 结算记录DTO
     * @param summaryRequest 费用明细请求
     * @return 结算单汇总信息
     */
    public PaymentSettlementRecordSummary generateSummaryFromDTO(PaymentRecordDTO paymentRecordDTO, PaymentSettlementSummaryRequest summaryRequest);

    /**
     * 根据费用明细请求保存或更新汇总信息
     *
     * @param summaryRequest 费用明细请求
     * @return 结果
     */
    public int saveOrUpdateSummaryFromRequest(PaymentSettlementSummaryRequest summaryRequest);

    /**
     * 导出结算单确认单docx文档
     *
     * @param paymentSettlementRecordSummary 结算单汇总信息
     * @param response HTTP响应
     */
    public void exportSettlementConfirmationReport(PaymentSettlementRecordSummary paymentSettlementRecordSummary, HttpServletResponse response);
}
