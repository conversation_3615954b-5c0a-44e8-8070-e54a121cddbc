package com.ruoyi.custom.admin.marketing.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.custom.admin.marketing.domain.PaymentSettlementRecordSummary;
import com.ruoyi.custom.admin.marketing.req.PaymentSettlementSummaryRequest;
import com.ruoyi.custom.admin.marketing.service.IPaymentSettlementRecordSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 结算单汇总信息Controller
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@RestController
@RequestMapping("/paymentSettlementRecordSummary")
@Api(value = "结算单汇总信息", tags = "结算单汇总信息")
public class PaymentSettlementRecordSummaryController extends BaseController {
    @Autowired
    private IPaymentSettlementRecordSummaryService paymentSettlementRecordSummaryService;

    /**
     * 查询结算单汇总信息列表
     */
    // @RequiresPermissions("custom:settlementSummary:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询结算单汇总信息列表")
    public TableDataInfo list(PaymentSettlementRecordSummary paymentSettlementRecordSummary) {
        startPage();
        List<PaymentSettlementRecordSummary> list = paymentSettlementRecordSummaryService.selectPaymentSettlementRecordSummaryList(paymentSettlementRecordSummary);
        return getDataTable(list);
    }

    /**
     * 导出结算单汇总信息列表
     */
    // @RequiresPermissions("custom:settlementSummary:export")
    @Log(title = "结算单汇总信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出结算单汇总信息列表")
    public void export(HttpServletResponse response, PaymentSettlementRecordSummary paymentSettlementRecordSummary) {
        List<PaymentSettlementRecordSummary> list = paymentSettlementRecordSummaryService.selectPaymentSettlementRecordSummaryList(paymentSettlementRecordSummary);
        ExcelUtil<PaymentSettlementRecordSummary> util = new ExcelUtil<PaymentSettlementRecordSummary>(PaymentSettlementRecordSummary.class);
        util.exportExcel(response, list, "结算单汇总信息数据");
    }

    /**
     * 获取结算单汇总信息详细信息
     */
    // @RequiresPermissions("custom:settlementSummary:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取结算单汇总信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(paymentSettlementRecordSummaryService.selectPaymentSettlementRecordSummaryById(id));
    }

    /**
     * 根据结算记录ID获取汇总信息
     */
    @GetMapping("/byPaymentRecordId/{paymentRecordId}")
    @ApiOperation(value = "根据结算记录ID获取汇总信息")
    public AjaxResult getInfoByPaymentRecordId(@PathVariable("paymentRecordId") String paymentRecordId) {
        PaymentSettlementRecordSummary summary = paymentSettlementRecordSummaryService.selectPaymentSettlementRecordSummaryByPaymentRecordId(paymentRecordId);
        return AjaxResult.success(summary);
    }

    /**
     * 新增结算单汇总信息
     */
    // @RequiresPermissions("custom:settlementSummary:add")
    @Log(title = "结算单汇总信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增结算单汇总信息")
    public AjaxResult add(@RequestBody PaymentSettlementRecordSummary paymentSettlementRecordSummary) {
        return toAjax(paymentSettlementRecordSummaryService.insertPaymentSettlementRecordSummary(paymentSettlementRecordSummary));
    }

    /**
     * 修改结算单汇总信息
     */
    // @RequiresPermissions("custom:settlementSummary:edit")
    @Log(title = "结算单汇总信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改结算单汇总信息")
    public AjaxResult edit(@RequestBody PaymentSettlementRecordSummary paymentSettlementRecordSummary) {
        return toAjax(paymentSettlementRecordSummaryService.updatePaymentSettlementRecordSummary(paymentSettlementRecordSummary));
    }

    /**
     * 删除结算单汇总信息
     */
    // @RequiresPermissions("custom:settlementSummary:remove")
    @Log(title = "结算单汇总信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除结算单汇总信息")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(paymentSettlementRecordSummaryService.deletePaymentSettlementRecordSummaryByIds(ids));
    }

    /**
     * 保存或更新结算单汇总信息（根据费用明细）
     */
    @Log(title = "结算单汇总信息", businessType = BusinessType.INSERT)
    @PostMapping("/saveFromRequest")
    @ApiOperation(value = "保存或更新结算单汇总信息（根据费用明细）")
    public AjaxResult saveFromRequest(@RequestBody PaymentSettlementSummaryRequest summaryRequest) {
        return toAjax(paymentSettlementRecordSummaryService.saveOrUpdateSummaryFromRequest(summaryRequest));
    }

    /**
     * 导出结算确认单docx文档
     */
    // @RequiresPermissions("custom:settlementSummary:export")
    @Log(title = "结算单汇总信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportSettlementConfirmation")
    @ApiOperation(value = "导出结算确认单docx文档")
    public void exportSettlementConfirmation(HttpServletResponse response, @RequestParam String paymentRecordId) {
        // 通过结算记录ID获取汇总信息
        PaymentSettlementRecordSummary paymentSettlementRecordSummary = paymentSettlementRecordSummaryService.selectPaymentSettlementRecordSummaryByPaymentRecordId(paymentRecordId);
        if (paymentSettlementRecordSummary == null) {
            throw new ServiceException("结算单汇总信息不存在");
        }
        paymentSettlementRecordSummaryService.exportSettlementConfirmationReport(paymentSettlementRecordSummary, response);
    }
}
