# 缴费记录票据号功能开发文档

## 功能概述

为 `ConfirmPaymentRequest` 添加票据号字段，实现使用票据号更新缴费记录ID的功能，并提供票据号查重机制。

## 修改内容

### 1. 数据库修改

**文件位置**: `sql/20250807/ry-custom.sql`

- 为 `t_payment_record` 表添加 `bill_number` 字段
- 添加票据号索引以提高查询性能
- 添加票据号唯一约束确保不重复

```sql
-- 添加票据号字段
ALTER TABLE `t_payment_record` 
ADD COLUMN `bill_number` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '票据号' AFTER `id`;

-- 添加索引
CREATE INDEX `idx_bill_number` ON `t_payment_record` (`bill_number`);

-- 添加唯一约束
ALTER TABLE `t_payment_record` 
ADD CONSTRAINT `uk_bill_number` UNIQUE (`bill_number`);
```

### 2. 实体类修改

**文件位置**: `ruoyi-modules/ruoyi-custom/src/main/java/com/ruoyi/custom/admin/marketing/domain/PaymentRecord.java`

- 添加 `billNumber` 字段及相关注解

```java
/**
 * 票据号
 */
@ApiModelProperty(value = "票据号")
private String billNumber;
```

### 3. 请求对象修改

**文件位置**: `ruoyi-modules/ruoyi-custom/src/main/java/com/ruoyi/custom/admin/marketing/controller/PaymentRecordController.java`

- 在 `ConfirmPaymentRequest` 中添加 `billNumber` 字段
- 修改控制器方法传递票据号参数

```java
@Data
@ApiModel(value = "ConfirmPaymentRequest", description = "确认缴费请求")
public static class ConfirmPaymentRequest {
    @ApiModelProperty(value = "票据号")
    private String billNumber;

    @ApiModelProperty(value = "实缴详情")
    private List<PaymentRecord.PaidDetail> paidDetails;

    @ApiModelProperty(value = "备注")
    private String remark;
}
```

### 4. 数据访问层修改

**文件位置**: 
- `ruoyi-modules/ruoyi-custom/src/main/java/com/ruoyi/custom/admin/marketing/mapper/PaymentRecordMapper.java`
- `ruoyi-modules/ruoyi-custom/src/main/resources/mapper/custom/marketing/PaymentRecordMapper.xml`

- 添加根据票据号查询的方法
- 更新 SQL 映射文件，添加票据号字段映射
- 在查询条件中支持票据号筛选

```java
/**
 * 根据票据号查询缴费确认单
 *
 * @param billNumber 票据号
 * @return 缴费确认单
 */
public PaymentRecord selectPaymentRecordByBillNumber(String billNumber);
```

### 5. 业务逻辑修改

**文件位置**: 
- `ruoyi-modules/ruoyi-custom/src/main/java/com/ruoyi/custom/admin/marketing/service/IPaymentRecordService.java`
- `ruoyi-modules/ruoyi-custom/src/main/java/com/ruoyi/custom/admin/marketing/service/impl/PaymentRecordServiceImpl.java`

- 修改 `confirmPayment` 方法签名，添加票据号参数
- 实现票据号查重逻辑
- 在确认缴费时更新票据号

```java
@Override
@Transactional(rollbackFor = Exception.class)
public int confirmPayment(String id, String billNumber, List<PaymentRecord.PaidDetail> paidDetails, String remark) {
    // 验证票据号
    if (StrUtil.isBlank(billNumber)) {
        throw new ServiceException("票据号不能为空");
    }

    // 检查票据号是否已存在（查重）
    PaymentRecord existingRecord = paymentRecordMapper.selectPaymentRecordByBillNumber(billNumber);
    if (existingRecord != null && !existingRecord.getId().equals(id)) {
        throw new ServiceException("票据号已存在，请检查后重新输入");
    }

    // 更新票据号
    draftRecord.setBillNumber(billNumber);
    
    // ... 其他业务逻辑
}
```

### 6. 单元测试

**文件位置**: `ruoyi-modules/ruoyi-custom/src/test/java/com/ruoyi/custom/admin/marketing/service/PaymentRecordServiceTest.java`

- 添加票据号功能的单元测试
- 测试正常流程、票据号为空、票据号重复等场景

## 功能特性

### 1. 票据号验证
- 票据号不能为空
- 票据号必须唯一，不允许重复

### 2. 查重机制
- 在确认缴费时检查票据号是否已存在
- 如果票据号已被其他记录使用，抛出异常提示

### 3. 数据完整性
- 数据库层面通过唯一约束保证票据号不重复
- 应用层面通过业务逻辑进行验证

## API 变更

### 确认缴费单接口

**接口路径**: `PUT /payment-record/confirm/{id}`

**请求参数变更**:
```json
{
    "billNumber": "BILL-2025080701",  // 新增字段：票据号
    "paidDetails": [
        {
            "paidCost": 1000.00,
            "paymentMethod": "1"
        }
    ],
    "remark": "确认缴费备注"
}
```

## 部署说明

1. **执行数据库脚本**: 运行 `sql/20250807/ry-custom.sql` 中的 SQL 语句
2. **重启应用**: 部署新版本代码并重启应用服务
3. **功能验证**: 通过前端或 API 测试票据号功能

## 注意事项

1. **数据迁移**: 现有数据的 `bill_number` 字段为 NULL，需要根据业务需求决定是否需要补充历史数据
2. **前端适配**: 前端需要在确认缴费界面添加票据号输入框
3. **错误处理**: 注意处理票据号重复的错误提示，给用户友好的反馈

## 作者信息

- **开发者**: zkx
- **开发日期**: 2025-08-07
- **版本**: v1.0.0
