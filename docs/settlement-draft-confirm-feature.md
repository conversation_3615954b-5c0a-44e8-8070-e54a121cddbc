# 结算流程暂存确认功能开发文档

## 功能概述

参考缴费流程的暂存、确认机制，为结算流程添加相同的暂存、确认概念，提供更灵活的结算操作流程。

## 功能特性

### 1. 结算单状态管理
- **暂存状态（0）**: 结算单已创建但未确认，可以修改和删除
- **已确认状态（1）**: 结算单已确认，触发账户变动和退住流程

### 2. 暂存结算单功能
- 保存结算单基本信息，不触发账户变动
- 不设置结算时间、票据号
- 可以多次修改暂存的结算单
- 支持删除暂存状态的结算单

### 3. 确认结算单功能
- 使用结算单ID作为票据号
- 触发账户变动和保障金变动
- 执行退住流程
- 更新结算时间
- 不需要实缴详情（结算单特有）

## 新增接口

### 1. 暂存结算单
**接口路径**: `POST /payment-record/settlement/draft`

**请求参数**:
```json
{
    "contractNumber": "HT-2025080701",
    "elderlyId": "123456",
    "elderlyName": "张三",
    "dischargeDate": "2025-08-07",
    "details": [
        {
            "type": "1",
            "typeName": "床位费",
            "refundAmount": 500.00,
            "paymentAmount": 0.00
        }
    ],
    "remark": "暂存备注"
}
```

**响应结果**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": "BILL-2025080701"
}
```

### 2. 确认结算单
**接口路径**: `PUT /payment-record/settlement/confirm/{id}`

**请求参数**: 无需请求体，仅通过路径参数传递ID

**响应结果**:
```json
{
    "code": 200,
    "msg": "操作成功"
}
```

### 3. 修改暂存结算单
**接口路径**: `PUT /payment-record/settlement/draft/{id}`

**请求参数**: 与暂存结算单相同

### 4. 删除暂存结算单
**接口路径**: `DELETE /payment-record/settlement/draft/{id}`

## 业务流程

### 暂存结算单流程
1. 调用生成结算信息接口获取结算详情
2. 调用暂存结算单接口保存基本信息
3. 可选择修改暂存的结算单
4. 最终确认结算单完成结算

### 确认结算单流程
1. 验证结算单状态（必须为暂存状态）
2. 设置票据号为结算单ID
3. 执行账户变动逻辑
4. 执行退住流程
5. 更新结算状态为已确认

## 数据验证

### 状态验证
- 只能确认暂存状态的结算单
- 只能修改暂存状态的结算单
- 只能删除暂存状态的结算单

### 结算单特殊性
- 结算单不需要票据号输入，使用ID作为票据号
- 结算单不需要实缴详情
- 结算单不需要实缴金额

## 兼容性

### 向后兼容
- 保留原有的直接确认结算接口 `POST /payment-record/settlement`
- 原有接口行为不变，直接创建已确认状态的结算单

### 数据库兼容
- 复用现有的 `t_payment_record` 表
- 使用 `fee_type` 字段区分缴费单（1）和结算单（2）
- 使用 `payment_status` 字段区分暂存（0）和已确认（1）状态

## 错误处理

### 常见错误场景
1. **记录不存在**: "暂存记录不存在" / "结算记录不存在"
2. **状态错误**: "只能确认暂存状态的结算单" / "只能修改暂存状态的结算单"

## 实现细节

### Service层方法
- `draftSettlement()`: 暂存结算单
- `confirmSettlement()`: 确认结算单（仅需ID参数）
- `updateDraftSettlement()`: 修改暂存结算单
- `deleteDraftSettlement()`: 删除暂存结算单

### Controller层接口
- `POST /settlement/draft`: 暂存结算单
- `PUT /settlement/confirm/{id}`: 确认结算单（无需请求体）
- `PUT /settlement/draft/{id}`: 修改暂存结算单
- `DELETE /settlement/draft/{id}`: 删除暂存结算单

## 使用示例

### 完整的结算流程示例
```javascript
// 1. 生成结算信息
const settlementInfo = await api.get('/payment-record/settlement/info', {
    params: { contractNumber: 'HT-2025080701' }
});

// 2. 暂存结算单
const draftResult = await api.post('/payment-record/settlement/draft', settlementInfo.data);
const settlementId = draftResult.data;

// 3. 修改暂存结算单（可选）
await api.put(`/payment-record/settlement/draft/${settlementId}`, {
    ...settlementInfo.data,
    remark: '修改后的备注'
});

// 4. 确认结算单（无需请求体）
await api.put(`/payment-record/settlement/confirm/${settlementId}`);
```

## 与缴费流程的区别

| 特性 | 缴费流程 | 结算流程 |
|------|----------|----------|
| 票据号 | 需要手动输入 | 使用ID作为票据号 |
| 实缴详情 | 需要 | 不需要 |
| 实缴金额 | 需要计算 | 不需要 |
| 确认参数 | 需要票据号、实缴详情、备注 | 仅需要ID |
| 业务逻辑 | 账户充值 | 账户结算、退住 |
