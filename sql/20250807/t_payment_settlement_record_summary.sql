-- ----------------------------
-- 结算单汇总信息表
-- 作者: zkx
-- 日期: 2025-08-07
-- 描述: 用于存储结算单的汇总信息，包括客户基本信息、结算金额、费用计算详情等
-- ----------------------------

CREATE TABLE `t_payment_settlement_record_summary` (
  `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键ID',
  `payment_record_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '结算记录ID，关联t_payment_record表',
  `customer_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '客户姓名',
  `live_status` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '入住状态',
  `care_level` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '护理级别',
  `bed_number` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '床位号',
  `total_payment` decimal(10,2) DEFAULT NULL COMMENT '结算合计',
  `room_type` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '房间类型',
  `contract_number` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '合同号',
  `discharge_date` date DEFAULT NULL COMMENT '离院日期',
  `discharge_date_formatted` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '离院日期（yyyy年MM月dd日）',
  `fee_calculation_details` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '费用计算详情',
  `del_flag` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏）',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_payment_record_id` (`payment_record_id`) USING BTREE COMMENT '结算记录ID索引',
  KEY `idx_customer_name` (`customer_name`) USING BTREE COMMENT '客户姓名索引',
  KEY `idx_contract_number` (`contract_number`) USING BTREE COMMENT '合同号索引',
  KEY `idx_discharge_date` (`discharge_date`) USING BTREE COMMENT '离院日期索引',
  KEY `idx_del_flag` (`del_flag`) USING BTREE COMMENT '删除标记索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='结算单汇总信息表';
