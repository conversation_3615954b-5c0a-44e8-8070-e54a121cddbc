-- ----------------------------
-- 为 t_payment_record 表添加票据号字段
-- 作者: zkx
-- 日期: 2025-08-07
-- 描述: 为确认缴费请求添加票据号字段，用于票据号查重和更新缴费记录ID
-- ----------------------------

-- 添加票据号字段
ALTER TABLE `t_payment_record` 
ADD COLUMN `bill_number` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '票据号' AFTER `id`;

-- 为票据号字段添加索引，提高查询性能
CREATE INDEX `idx_bill_number` ON `t_payment_record` (`bill_number`);

-- 为票据号字段添加唯一约束，确保票据号不重复
ALTER TABLE `t_payment_record` 
ADD CONSTRAINT `uk_bill_number` UNIQUE (`bill_number`);
